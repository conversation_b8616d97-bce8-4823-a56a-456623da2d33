package com.mingdao.edge.plugin.gateway.service;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.ark.api.ClientResponse;
import com.mingdao.edge.core.api.biz.BizCache;
import com.mingdao.edge.core.api.biz.EdgeGatewayService;
import com.mingdao.edge.core.api.biz.dto.BizInfo;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.mqtt.utils.RegisterMqttClientUtil;
import com.mingdao.edge.plugin.api.gateway.dto.DataTaskEdgeBiz;
import com.mingdao.edge.plugin.api.gateway.dto.TenantMqttProperty;
import com.mingdao.edge.plugin.gateway.api.ServerApiImpl;
import com.mingdao.edge.plugin.gateway.callback.OnMqttMessageCallback;
import com.mingdao.edge.plugin.gateway.constants.BizConstant;
import com.mingdao.edge.plugin.gateway.holder.BizInitParamsHolder;
import com.mingdao.edge.plugin.gateway.holder.TenantHolder;
import com.mingdao.edge.plugin.gateway.util.BizFileUtil;
import com.mingdao.edge.plugin.gateway.util.BizHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc desc
 */
@Slf4j
@Service
public class StartUpService {

    @Resource
    private ServerApiImpl serverApiImpl;
    @Resource
    private OnMqttMessageCallback onMqttMessageCallback;
    @Resource
    private EdgeCacheManager edgeCacheManager;
    @Resource
    private EdgeGatewayService edgeGatewayService;
    @Resource
    private BizInitParamsHolder bizInitParamsHolder;
    @Resource
    private BizCacheImpl bizCacheImpl;

    private final TypeReference<List<DataTaskEdgeBiz>> typeReference = new TypeReference<List<DataTaskEdgeBiz>>() {};
    private boolean first_run = true;

    public void initMqtt() throws MqttException {
        TenantMqttProperty tenantMqttProperty = getRefreshMqttProperty();
        if (tenantMqttProperty != null) {
            // 不为空，即是需要刷新订阅信息，将重新进行订阅
            RegisterMqttClientUtil.registerMqttClient(
                    tenantMqttProperty.getBroker(),
                    SpringContextUtils.getApplicationName() + "-" + tenantMqttProperty.getUserId(),
                    tenantMqttProperty.getUserName(),
                    tenantMqttProperty.getPassword(),
                    Collections.singletonList(StrUtil.format("edge/cmd/#", tenantMqttProperty.getOrgCode())),
                    onMqttMessageCallback);
        }
    }

    public void initBizInfo() {
        List<DataTaskEdgeBiz> bizInfoList = getRefreshBizInfo();
        if (bizInfoList != null && !bizInfoList.isEmpty()) {
            for (DataTaskEdgeBiz entry : bizInfoList) {
                String bizName = entry.getBIZ_NAME();
                String version = entry.getVERSION();
                String downloadUrl = entry.getFILE_URL();

                BizInfo bizInfo = new BizInfo(bizName, version, downloadUrl);
                String filePath = bizInfo.getLocalPath();
                if (!BizFileUtil.existsBiz(filePath)) {
                    if (StrUtil.isNotEmpty(downloadUrl)) {
                        BizHttpUtil.downLoad(downloadUrl, filePath).ifPresent(path -> {
                            log.info("下载完成 {}", downloadUrl);
                        });
                    }
                }
                ClientResponse clientResponse = edgeGatewayService.installBiz(bizInfo);
                log.info(clientResponse.getMessage());
            }
        }
    }

    public TenantMqttProperty getRefreshMqttProperty() {
        TenantMqttProperty cache_tenantMqttProperty = edgeCacheManager.get(BizConstant.CACHE_KEY_MQTT_PROPERTY, TenantMqttProperty.class);

        TenantMqttProperty _tenantMqttProperty = serverApiImpl.getTenantMqttProperty();

        if (first_run || !(cache_tenantMqttProperty != null
                && _tenantMqttProperty != null
                && ObjectUtil.equals(_tenantMqttProperty, cache_tenantMqttProperty))) {
            first_run = false;
            edgeCacheManager.set(BizConstant.CACHE_KEY_MQTT_PROPERTY, _tenantMqttProperty);
            return _tenantMqttProperty;
        }

        return null;
    }

    public List<DataTaskEdgeBiz> getRefreshBizInfo() {

        List<DataTaskEdgeBiz> cache_bizInfoList = getCacheBizInfo();

        List<DataTaskEdgeBiz> bizInfoList = serverApiImpl.getTenantBiz();

        boolean isSame = cache_bizInfoList != null && !cache_bizInfoList.isEmpty()
                && bizInfoList != null && !bizInfoList.isEmpty()
                && ObjectUtil.equals(bizInfoList, cache_bizInfoList);

        if (!isSame) {
            Optional.ofNullable(bizInfoList)
                    .flatMap(list -> list.stream().findFirst())
                    .ifPresent(item -> {
                        TenantHolder.setSysOrg(item.getSYS_ORG());
                        bizCacheImpl.setInitParams(item.getBIZ_NAME(), item.getVERSION(), BizCache.kEY_INIT_PARAMS, item.getINIT_PARAMS());
                    });
            edgeCacheManager.set(BizConstant.CACHE_KEY_BIZ_LIST, bizInfoList);
        }

        return bizInfoList;
    }

    public List<DataTaskEdgeBiz> getCacheBizInfo() {
        return edgeCacheManager.get(BizConstant.CACHE_KEY_BIZ_LIST, typeReference);
    }

}
