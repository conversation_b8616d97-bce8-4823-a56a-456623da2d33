package com.mingdao.edge.plugin.gateway.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.gateway.holder.GatewayDownHandlerHolder;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttMessageListener;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Component
public class OnMqttMessageCallback implements IMqttMessageListener {

    @Resource
    private GatewayDownHandlerHolder gatewayDownHandlerHolder;

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) {

        //log.info("接收消息主题:" + topic);
        //log.info("接收消息Qos:" + mqttMessage.getQos());
        //log.info("接收消息内容:" + new String(mqttMessage.getPayload()));

        try {
            DownMessage<?> downMessage = JSON.parseObject(mqttMessage.getPayload(), DownMessage.class);
            if (downMessage == null) {
                log.warn("[网关下行消息]异常：缺少实体内容");
                return;
            }

            MessageType downType = downMessage.getDownType();
            if (downType != null) {
                if (MessageType.CMD_GATEWAY_DOWN.equals(downType)) {
                    CommandDto<?> commandDto;
                    Object downMessageData = downMessage.getData();
                    if (downMessageData instanceof JSONObject) {
                        commandDto = ((JSONObject) downMessageData).toJavaObject(CommandDto.class);
                    } else if (downMessageData instanceof CommandDto) {
                        commandDto = (CommandDto<?>) downMessageData;
                    } else {
                        commandDto = JSON.parseObject(JSON.toJSONString(downMessageData), CommandDto.class);
                    }

                    //String downHandler = downMessage.getDownHandler();
                    if (commandDto != null) {
                        String command = commandDto.getCommand();
                        String beanName = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + command;
                        GatewayDownHandler gatewayDownHandler = gatewayDownHandlerHolder.getService(beanName, GatewayDownHandler.class);
                        gatewayDownHandler.process(downMessage.getMessageId(), commandDto);
                    }
                }
            }



        } catch (Exception e) {
            log.error("[网关下行消息]异常: " + e.getMessage(), e);
        }
    }
}
