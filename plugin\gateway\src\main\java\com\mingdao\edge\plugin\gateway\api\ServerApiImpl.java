package com.mingdao.edge.plugin.gateway.api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.constants.RequestResult;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.plugin.api.gateway.ServerApi;
import com.mingdao.edge.plugin.api.gateway.dto.DataTaskEdgeBiz;
import com.mingdao.edge.plugin.api.gateway.dto.MSSQLProperty;
import com.mingdao.edge.plugin.api.gateway.dto.TenantMqttProperty;
import com.mingdao.edge.plugin.gateway.constants.CacheKey;
import com.mingdao.edge.plugin.gateway.property.CloudApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
public class ServerApiImpl extends AbstractApiImpl implements ServerApi {

    @Resource
    private CloudApiProperties cloudApiProperties;
    @Resource
    private EdgeCacheManager edgeCacheManager;

    private final TypeReference<BaseResponse<Map<String, String>>> T_MAP = new TypeReference<BaseResponse<Map<String, String>>>() {};

    private final TypeReference<BaseResponse<TenantMqttProperty>> T = new TypeReference<BaseResponse<TenantMqttProperty>>() {};
    private final TypeReference<BaseResponse<List<DataTaskEdgeBiz>>> T_DataTaskEdgeBiz = new TypeReference<BaseResponse<List<DataTaskEdgeBiz>>>() {};

    public TenantMqttProperty getTenantMqttProperty() {
        TenantMqttProperty tenantMqttProperty = edgeCacheManager.get(CacheKey.MQTT_PROPERTIES, TenantMqttProperty.class);
        if (tenantMqttProperty == null) {
            String requestPath = "/edge/mqtt/info";

            Map<String, Object> params = new LinkedHashMap<>();
            params.put("userId", cloudApiProperties.getInitUser());

            BaseResponse<TenantMqttProperty> response = postWithToken(cloudApiProperties.getDomain() + requestPath, params, T);
            if (response != null && RequestResult.RES_SUCCESS.equals(response.getCode())) {
                tenantMqttProperty = response.getData();
                if (tenantMqttProperty != null) {
                    String profile = SpringContextUtils.getActiveProfile();
                    if ("dev".equals(profile)) {
                        TenantMqttProperty devTenantMqttProperty = new TenantMqttProperty();
                        devTenantMqttProperty.setBroker("tcp://*********:1883");
                        devTenantMqttProperty.setUserName("mingdao");
                        devTenantMqttProperty.setPassword("123456");
                        devTenantMqttProperty.setUserId(tenantMqttProperty.getUserId());
                        devTenantMqttProperty.setOrgCode(tenantMqttProperty.getOrgCode());
                        tenantMqttProperty = devTenantMqttProperty;
                    }
                    edgeCacheManager.set(CacheKey.MQTT_PROPERTIES, tenantMqttProperty, 5 * 60);
                }

            }
        }

        return tenantMqttProperty;
    }


    public List<DataTaskEdgeBiz> getTenantBiz() {
        String requestPath = "/edge/plugin/list";

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("userId", cloudApiProperties.getInitUser());

        //String profile = SpringContextUtils.getActiveProfile();
        //if ("dev".equals(profile)) {
        //    return getDevTenantBiz();
        //}

        BaseResponse<List<DataTaskEdgeBiz>> response = postWithToken(cloudApiProperties.getDomain() + requestPath, params, T_DataTaskEdgeBiz);
        if (response != null && RequestResult.RES_SUCCESS.equals(response.getCode())) {
            return response.getData();
        } else {
            return null;
        }
    }

    private List<DataTaskEdgeBiz> getDevTenantBiz() {
        List<DataTaskEdgeBiz> list = new ArrayList<>();

        DataTaskEdgeBiz dataSourceBiz = new DataTaskEdgeBiz();
        dataSourceBiz.setBIZ_NAME("data-source-driver-mssql");
        dataSourceBiz.setVERSION("1.0.0");
        dataSourceBiz.setSEQ(1);
        dataSourceBiz.setAUTOID(1L);
        dataSourceBiz.setSYS_ORG(140L);
        dataSourceBiz.setFILE_URL("https://test.mingdao-info.com/download/data-source-driver-mssql-1.0.0-ark-biz.jar");

        MSSQLProperty mssqlProperty = new MSSQLProperty();
        mssqlProperty.setMssqlHost("*************");
        mssqlProperty.setUserName("sa");
        mssqlProperty.setPassword("Abc123");
        mssqlProperty.setDataBaseName("u8Test");
        dataSourceBiz.setINIT_PARAMS(JSONObject.toJSONString(mssqlProperty));

        list.add(dataSourceBiz);


        //DataTaskEdgeBiz dataSourceBiz1 = new DataTaskEdgeBiz();
        //dataSourceBiz1.setBIZ_NAME("dynamic-provider");
        //dataSourceBiz1.setVERSION("2.0.0");
        //dataSourceBiz1.setSEQ(1);
        //dataSourceBiz1.setAUTOID(1L);
        //dataSourceBiz1.setSYS_ORG(140L);
        //
        //list.add(dataSourceBiz1);

        return list;

    }
}
