package com.mingdao.edge.plugin.gateway.holder;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.core.FileAppender;
import cn.hutool.core.util.StrUtil;
import com.mingdao.edge.core.mqtt.service.MqttService;
import com.mingdao.edge.plugin.api.gateway.dto.UpMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.gateway.dto.LogFetchCmd;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
public class LogHolder {

    private final Integer MAX_LINES;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final MqttService mqttService;

    private final String sessionId;
    private final String topic;
    private final String logPath;
    private final LinkedBlockingDeque<String> latestLogs;

    private long lastFilePointer = 0L;

    public LogHolder(MqttService mqttService, String topic, LogFetchCmd logFetchCmd) {
        this.mqttService = mqttService;
        this.topic = topic;
        this.sessionId = logFetchCmd.getSessionId();
        Integer maxLines = logFetchCmd.getLines();
        if (maxLines < 1 || maxLines > 10000) {
            maxLines = 10;
        }
        this.MAX_LINES = maxLines;
        latestLogs = new LinkedBlockingDeque<>(maxLines);

        logPath = getLogFilePath();
        // 首次读取最后100行
        List<String> initLogs = readLastLines(10, null);

        latestLogs.addAll(initLogs);
        // 设置lastFilePointer为文件末尾
        File file = new File(logPath);
        if (file.exists()) {
            lastFilePointer = file.length();
        }
        // 启动定时任务，每秒读取新增日志
        scheduler.scheduleAtFixedRate(this::refreshLogs, 0, 1, TimeUnit.SECONDS);
        scheduler.scheduleAtFixedRate(this::uploadLogs, 0, 1, TimeUnit.SECONDS);
        log.info("已启动日志");
    }

    private String getLogFilePath() {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger rootLogger = context.getLogger("ROOT");
        FileAppender appender = (FileAppender) rootLogger.getAppender("ALL");
        if (appender != null) {
            return appender.getFile();
        }
        return null;
    }

    private void refreshLogs() {
        readNewLines(logPath, lastFilePointer, null);
    }

    // 增量读取新增日志行，支持关键字过滤
    private void readNewLines(String filePath, long fromPointer, String keywords) {
        File file = new File(filePath);
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(fromPointer);
            long startPointer = raf.getFilePointer();
            // 用字节流读取新增内容
            byte[] buffer = new byte[(int) (file.length() - startPointer)];
            raf.readFully(buffer);
            if (buffer.length > 0) {
                String content = new String(buffer, StandardCharsets.UTF_8);
                String[] linesArr = content.split("\r?\n");
                for (String line : linesArr) {
                    if (!StrUtil.isEmpty(line) && (keywords == null || keywords.isEmpty() || line.contains(keywords))) {
                        latestLogs.offer(line);
                    }
                }
                lastFilePointer = raf.getFilePointer();
            }
        } catch (Exception e) {
            log.error("读取日志文件失败 {}", e.getMessage(), e);
        }
    }

    private void uploadLogs() {
        try {
            if (!latestLogs.isEmpty()) {
                List<String> list = new LinkedList<>();
                while (!latestLogs.isEmpty()) {
                    list.add(latestLogs.poll());
                }
                UpMessage<List<String>> upMessage = new UpMessage<>(sessionId, MessageType.LOG_FETCH_UP, list);
                mqttService.publish(topic, upMessage);
            }
        } catch (Exception e) {
            log.error("发送日志异常 {}", e.getMessage(), e);
        }
    }

    // 读取最后N行（用于首次窗口）
    public List<String> readLastLines(int lines, String keywords) {
        LinkedList<String> result = new LinkedList<>();
        File file = new File(logPath);
        if (!file.exists() || !file.isFile()) {
            result.add("Log file not found.");
            return result;
        }
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            long fileLength = raf.length();
            long pointer = fileLength - 1;
            int lineCount = 0;
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            while (pointer >= 0 && lineCount < lines) {
                raf.seek(pointer);
                int readByte = raf.readByte();
                if (readByte == '\n') {
                    if (baos.size() > 0) {
                        byte[] lineBytes = baos.toByteArray();
                        baos.reset();
                        // 反转字节数组
                        for (int i = 0, j = lineBytes.length - 1; i < j; i++, j--) {
                            byte tmp = lineBytes[i];
                            lineBytes[i] = lineBytes[j];
                            lineBytes[j] = tmp;
                        }
                        String decodedLine = new String(lineBytes, StandardCharsets.UTF_8);
                        if (keywords == null || keywords.isEmpty() || decodedLine.contains(keywords)) {
                            result.addFirst(decodedLine);
                            lineCount++;
                        }
                    }
                } else if (readByte != '\r') {
                    baos.write(readByte);
                }
                pointer--;
            }
            // 文件开头最后一行
            if (baos.size() > 0 && lineCount < lines) {
                byte[] lineBytes = baos.toByteArray();
                // 反转字节数组
                for (int i = 0, j = lineBytes.length - 1; i < j; i++, j--) {
                    byte tmp = lineBytes[i];
                    lineBytes[i] = lineBytes[j];
                    lineBytes[j] = tmp;
                }
                String decodedLine = new String(lineBytes, StandardCharsets.UTF_8);
                if (keywords == null || keywords.isEmpty() || decodedLine.contains(keywords)) {
                    result.addFirst(decodedLine);
                }
            }
        } catch (Exception e) {
            result.add("Error reading log file: " + e.getMessage());
        }
        return result;
    }

    /**
     * 释放资源，关闭定时任务
     */
    public void dispose() {
        try {
            log.info("LogHolder dispose called, shutting down scheduler.");
            scheduler.shutdownNow();
            latestLogs.clear();
        } catch (Exception e) {
            log.error("LogHolder dispose error.", e);
        }
    }
}
