package com.mingdao.edge.plugin.gateway.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.core.mqtt.service.MqttService;
import com.mingdao.edge.plugin.api.gateway.GatewayMqttPublishService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@SofaService
public class GatewayMqttPublishServiceImpl implements GatewayMqttPublishService {

    @Resource
    private MqttService mqttService;

    @Override
    public <T> void publish(String topic, T payload, int qos, boolean retained) {
        mqttService.publish(topic, payload, qos, retained);
    }

    @Override
    public <T> void publish(String topic, T payload) {
        mqttService.publish(topic, payload);
    }

    @Override
    public void publishRetained(String topic, JSONObject payload) {
        mqttService.publishRetained(topic, payload);
    }
}
