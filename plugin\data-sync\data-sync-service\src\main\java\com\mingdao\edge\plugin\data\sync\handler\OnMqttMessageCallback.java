package com.mingdao.edge.plugin.data.sync.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.api.gateway.dto.DataDto;
import com.mingdao.edge.plugin.api.gateway.dto.DownMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.data.sync.context.TaskContext;
import com.mingdao.edge.plugin.data.sync.context.TaskContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttMessageListener;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Component
public class OnMqttMessageCallback implements IMqttMessageListener {
    @Resource
    private TaskContextHolder taskContextHolder;

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) {

        //log.info("接收消息主题:" + topic);
        //log.info("接收消息Qos:" + mqttMessage.getQos());
        //log.info("接收消息内容:" + new String(mqttMessage.getPayload()));

        try {
            DownMessage<?> downMessage = JSON.parseObject(mqttMessage.getPayload(), DownMessage.class);
            Assert.notNull(downMessage, "缺少实体内容");

            Object downMessageData = downMessage.getData();
            JSONObject downMessageDataJson = null;
            if (downMessageData instanceof JSONObject) {
                downMessageDataJson = (JSONObject) downMessageData;
            } else if (downMessageData instanceof LinkedHashMap) {
                downMessageDataJson = new JSONObject((LinkedHashMap) downMessageData);
            }

            Assert.notNull(downMessageDataJson, "数据转换失败 {} {}", topic, downMessage);

            MessageType downType = downMessage.getDownType();

            if (MessageType.DATA_UP_CALLBACK.equals(downType)) {
                DataDto<?> dataDto = downMessageDataJson.toJavaObject(DataDto.class);
                Assert.notNull(dataDto, "数据实体转换失败");

                String dtName = dataDto.getDataTypeName();

                TaskContext taskContext = taskContextHolder.getTaskContext(dtName);

                DataSyncService dataSyncService = taskContext.getDataSyncService();
                Assert.notNull(dataSyncService, "缺少消息处理器");
                if (downMessage.getDownType() == MessageType.DATA_UP_CALLBACK) {
                    log.info("消费到数据状态回调 {}", JSON.toJSONString(downMessage));
                    dataSyncService.transferDataCallback(downMessage);
                }
            }

        } catch (Exception e) {
            log.error("[消费下行消息]异常: " + e.getMessage(), e);
        }
    }
}
