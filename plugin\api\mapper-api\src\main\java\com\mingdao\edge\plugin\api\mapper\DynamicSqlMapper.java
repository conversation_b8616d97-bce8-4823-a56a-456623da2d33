package com.mingdao.edge.plugin.api.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 动态SQL Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Mapper
public interface DynamicSqlMapper {

    /**
     * 执行查询SQL
     */
    List<Map<String, Object>> selectBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行分页查询SQL
     */
    IPage<Map<String, Object>> selectPageBySql(@Param("sql") String sql,
                                              @Param("page") Page<Map<String, Object>> page,
                                              @Param("params") Map<String, Object> params);

    /**
     * 执行插入SQL
     */
    int insertBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行更新SQL
     */
    int updateBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 执行删除SQL
     */
    int deleteBySql(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    int deleteByIds(@Param("tableName") String tableName, @Param("ids") Collection<Long> ids);
}
