package com.mingdao.edge.plugin.gateway.job;

import com.alipay.sofa.ark.api.ClientResponse;
import com.alipay.sofa.ark.spi.model.BizState;
import com.mingdao.edge.core.api.biz.EdgeGatewayService;
import com.mingdao.edge.core.api.biz.dto.BizInfo;
import com.mingdao.edge.plugin.api.gateway.dto.DataTaskEdgeBiz;
import com.mingdao.edge.plugin.api.gateway.enums.StateEnum;
import com.mingdao.edge.plugin.gateway.holder.TenantHolder;
import com.mingdao.edge.plugin.gateway.service.StartUpService;
import com.mingdao.edge.plugin.gateway.service.impl.EdgeStateServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProtectJob {

    private final StartUpService startUpService;
    private final EdgeGatewayService edgeGatewayService;
    private final EdgeStateServiceImpl edgeStateServiceImpl;

    @Scheduled(fixedRateString = "${mingdao.edge.healthcheck.get-biz-info-interval:300000}", initialDelay = 5 * 60 * 1000)
    public void getBizInfo() {
        try {
            startUpService.initMqtt();
        } catch (Exception e) {
            log.error("获取mqtt信息失败: " + e.getMessage(), e);
        }

    }

    @Scheduled(fixedRateString = "${mingdao.edge.healthcheck.check-interval:30000}", initialDelay = 5 * 60 * 1000)
    public void healthcheck() {
        // 先将缓存的biz放到队列中
        List<DataTaskEdgeBiz> bizInfoList = startUpService.getRefreshBizInfo();
        if (bizInfoList != null && !bizInfoList.isEmpty()) {
            for (DataTaskEdgeBiz dataTaskEdgeBiz : bizInfoList) {
                String bizName = dataTaskEdgeBiz.getBIZ_NAME();
                String version = dataTaskEdgeBiz.getVERSION();
                String downloadUrl = dataTaskEdgeBiz.getFILE_URL();
                try {
                    BizState bizState = edgeGatewayService.getBizState(bizName, version);
                    log.info("healthcheck biz {}, {} {} {}", bizName, version, bizState.getBizState(), TenantHolder.getSysOrg());
                    edgeStateServiceImpl.putState(StateEnum.EDGE_BIZ_STATE_TIME, bizName, bizState.getBizState(), new Date(), false, TenantHolder.getSysOrg());
                    if (BizState.BROKEN.equals(bizState) || BizState.UNRESOLVED.equals(bizState)) {
                        // 安装失败，才重试安装；卸载失败、安装中、未安装、卸载成功的，不在此处安装
                        BizInfo bizInfo = new BizInfo(bizName, version, downloadUrl);
                        ClientResponse clientResponse = edgeGatewayService.installBiz(bizInfo);
                        log.info(clientResponse.getMessage());
                    }
                } catch (Exception e) {
                    log.error("healthcheck error: " + e.getMessage(), e);
                }
            }
        }

    }
}
