package com.mingdao.edge.plugin.data.sync;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * description DataSyncApplication
 *
 * <AUTHOR>
 * @since 2025-06-06 17:17:39
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.mingdao.edge")
public class DataSyncApplication {
    public static void main(String[] args) {
        System.setProperty("org.springframework.boot.logging.LoggingSystem", "none");
        SpringApplication.run(DataSyncApplication.class, args);
    }
}
