package com.mingdao.edge.plugin.data.sync.handler.cmd;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mingdao.edge.core.common.dto.BaseResponse;
import com.mingdao.edge.core.common.exception.Assert;
import com.mingdao.edge.core.mqtt.service.MqttService;
import com.mingdao.edge.plugin.api.data.sync.dto.QueryEdgeDataDto;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import com.mingdao.edge.plugin.api.gateway.constants.GatewayConstants;
import com.mingdao.edge.plugin.api.gateway.dto.CommandDto;
import com.mingdao.edge.plugin.api.gateway.dto.UpMessage;
import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import com.mingdao.edge.plugin.api.mapper.service.DataBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@SofaService(uniqueId = GatewayConstants.GATEWAY_CMD_BEAN_PREFIX + "query_data")
public class QueryDataHandler implements GatewayDownHandler {

    @SofaReference
    private DataBaseService dataBaseService;
    @Resource
    private MqttService mqttService;

    @Override
    public Object process(String messageId, CommandDto<?> commandDto) {
        try {
            Assert.notNull(commandDto.getReplyTopic(), "参数不能为空");

            Object data = commandDto.getData();
            QueryEdgeDataDto queryEdgeDataDto = null;
            if (data instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) commandDto.getData();
                queryEdgeDataDto = jsonObject.toJavaObject(QueryEdgeDataDto.class);
            } else if (data instanceof QueryEdgeDataDto) {
                queryEdgeDataDto = (QueryEdgeDataDto) data;
            }

            Assert.notNull(queryEdgeDataDto, "查询参数不能为空");
            Assert.notNull(queryEdgeDataDto.getDtName(), "查询参数不能为空");
            Assert.notNull(queryEdgeDataDto.getSysOrg(), "查询参数不能为空");

            if (queryEdgeDataDto.getPage() == null || queryEdgeDataDto.getPage() <= 0) {
                queryEdgeDataDto.setPage(1);
            }
            if (queryEdgeDataDto.getLimit() == null
                    || queryEdgeDataDto.getLimit() <= 0) {
                queryEdgeDataDto.setLimit(20);
            } else if (queryEdgeDataDto.getLimit() > 1000) {
                queryEdgeDataDto.setLimit(1000);
            }

            String tableName = "tb_" + queryEdgeDataDto.getDtName();
            StringBuilder querySql = new StringBuilder("select * from ").append(tableName).append(" where 1=1");
            Map<String, Object> params = queryEdgeDataDto.getParams();
            if (params != null && !params.isEmpty()) {
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    String columnName = entry.getKey();
                    querySql.append(" and ").append(columnName).append(" = #{params.").append(columnName).append("}");
                }
            }
            IPage<Map<String, Object>> pageData = dataBaseService.selectPageBySql(querySql.toString(), queryEdgeDataDto.getPage(), queryEdgeDataDto.getLimit(), params);

            BaseResponse<Object> response = new BaseResponse<>();
            response.setData(pageData.getRecords());
            response.setCount(pageData.getTotal());

            UpMessage<BaseResponse<Object>> upMessage = new UpMessage<>(MessageType.DATA_QUERY_UP, response);
            mqttService.publish(commandDto.getReplyTopic(), upMessage);

        } catch (Exception e) {
            log.error(StrUtil.format("执行指令异常 {} {}", "query_data", e.getMessage()), e);
        }
        return null;
    }
}
