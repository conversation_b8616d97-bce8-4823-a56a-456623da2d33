package com.mingdao.edge.plugin.api.gateway;

import com.alibaba.fastjson.JSONObject;

/**
 * 通过网关发送mqtt消息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface GatewayMqttPublishService {
    /**
     * 发布消息
     * @param topic 主题
     * @param payload 消息内容
     * @param qos QoS等级
     * @param retained 是否保留
     */
    <T> void publish(String topic, T payload, int qos, boolean retained);
    /**
     * 发布消息，默认qos=1，retained=false
     * @param topic 主题
     * @param payload 消息内容
     */
    <T> void publish(String topic, T payload);
    /**
     * 发布消息，默认qos=1，retained=false
     * @param topic 主题
     * @param payload 消息内容
     */
    void publishRetained(String topic, JSONObject payload);
}
