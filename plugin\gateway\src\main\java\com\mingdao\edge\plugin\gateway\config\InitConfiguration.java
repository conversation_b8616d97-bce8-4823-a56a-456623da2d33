package com.mingdao.edge.plugin.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * description MinaConfig
 *
 * <AUTHOR>
 * @since 2022/7/12 14:48
 */
@Slf4j
@Configuration
public class InitConfiguration {

    //@Bean
    //@SofaService
    //EdgeGatewayDataReceiveRelay connectorMessageHandler() {
    //    return new EdgeGatewayDataReceiveRelayImpl();
    //}

}
