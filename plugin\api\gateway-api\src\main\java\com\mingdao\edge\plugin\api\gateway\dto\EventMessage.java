package com.mingdao.edge.plugin.api.gateway.dto;

import com.mingdao.edge.plugin.api.gateway.enums.MessageType;
import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
public class EventMessage<T> implements Serializable {
    private String dtName;
    private String handlerName;
    private String publishTopic;

    private MessageType messageType;

    private T data;
}
