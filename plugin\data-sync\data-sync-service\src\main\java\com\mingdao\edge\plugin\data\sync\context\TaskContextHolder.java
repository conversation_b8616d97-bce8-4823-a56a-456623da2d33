package com.mingdao.edge.plugin.data.sync.context;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.data.sync.base.constants.DataSyncConstants;
import com.mingdao.edge.plugin.data.sync.config.DynamicTaskScheduler;
import com.mingdao.edge.plugin.data.sync.holder.DataSyncReferenceHolder;
import com.mingdao.edge.plugin.data.sync.util.CronExpressionGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component
public class TaskContextHolder {

    private final static Map<String, TaskContext> TASK_CONTEXT_MAP = new HashMap<>();

    @Resource
    private DynamicTaskScheduler dynamicTaskScheduler;
    @Resource
    private DataSyncReferenceHolder dataSyncReferenceHolder;

    private TaskContext getContext(String uniqueId) {
        return TASK_CONTEXT_MAP.get(uniqueId);
    }

    private boolean contains(String uniqueId) {
        return TASK_CONTEXT_MAP.containsKey(uniqueId);
    }

    private void addContext(String uniqueId, TaskContext taskContext) {
        TASK_CONTEXT_MAP.put(uniqueId, taskContext);
    }

    private void removeContext(String uniqueId) {
        TASK_CONTEXT_MAP.remove(uniqueId);
    }

    public TaskContext getTaskContext(String dtName) {
        String uniqueId = DataSyncConstants.DATA_SYNC_SERVICE + "_" + dtName;
        return TASK_CONTEXT_MAP.get(uniqueId);
    }

    public TaskContext getFirstTaskContext() {
        return TASK_CONTEXT_MAP.values().stream().findFirst().orElse(null);
    }

    public void createTaskContext(JSONObject taskInfo) {

        String dtName = taskInfo.getString("dt_name");
        String uniqueId = DataSyncConstants.DATA_SYNC_SERVICE + "_" + dtName;

        if (contains(uniqueId)) {
            return;
        }

        log.info("[初始化任务]开始 {}", uniqueId);

        DataSyncService dataSyncService = dataSyncReferenceHolder.getService(uniqueId, DataSyncService.class);
        if (dataSyncService != null) {

            TaskContext taskContext = new TaskContext();
            taskContext.setUniqueId(uniqueId);

            try {
                dataSyncService.init(taskInfo);
                taskContext.setUpTopic(dataSyncService.getUpTopic());
            } catch (Exception e) {
                log.warn("[初始化任务]失败: {} {}", uniqueId, e.getMessage());
                dataSyncReferenceHolder.removeService(uniqueId);
                return;
            }

            // 创建定时任务
            Integer dtoRunMinute = taskInfo.getInteger("dto_runminute");
            String cron = CronExpressionGenerator.intervalMinutes(dtoRunMinute);
            ScheduledFuture<?> scheduledFuture = dynamicTaskScheduler.buildTask(uniqueId, () -> {
                dataSyncService.getData();
            }, cron);

            taskContext.setScheduledFuture(scheduledFuture);
            taskContext.setDataSyncService(dataSyncService);

            taskContext.setTaskInfo(taskInfo);

            addContext(uniqueId, taskContext);

            log.info("[初始化任务] {} 成功", uniqueId);
        }

        log.info("[初始化任务]结束 {}", uniqueId);
    }

    public void removeTaskContext(String uniqueId) {
        TaskContext taskContext = getContext(uniqueId);
        if (taskContext != null) {
            disposeContext(taskContext);
            removeContext(uniqueId);
        }
    }

    public void disposeContext(TaskContext taskContext) {
        ScheduledFuture<?> scheduledFuture = taskContext.getScheduledFuture();
        if (scheduledFuture != null) {
            try {
                scheduledFuture.cancel(true);
            } finally {
                log.info("移除数据同步任务: " + taskContext.getUniqueId());
                dataSyncReferenceHolder.removeService(taskContext.getUniqueId());
            }
        }
    }



    @PreDestroy
    public void beforeDestroy() {
        try {
            if (!TASK_CONTEXT_MAP.isEmpty()) {
                for (String key : TASK_CONTEXT_MAP.keySet()) {
                    removeTaskContext(key);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
