package com.mingdao.edge.plugin.api.data.sync.constants;

public interface DataState {
    /**
     * 待转换 写入数据库时的状态
     */
    String WAITING_CONVERT = "待转换";
    /**
     * 待上传
     */
    String WAITING_UPLOAD = "待上传";
    /**
     * 上传成功
     */
    String UPLOAD_SUCCESS = "上传成功";
    /**
     * 上传失败
     */
    String UPLOAD_FAIL = "上传失败";
    /**
     * 错误
     */
    String ERROR = "错误";
    /**
     * 重复数据
     */
    String DUPLICATE = "重复数据";
    /**
     * 已同步
     */
    String SYNC_SUCCESS = "已同步";
    /**
     * 同步失败
     */
    String SYNC_FAIL = "同步失败";
}
