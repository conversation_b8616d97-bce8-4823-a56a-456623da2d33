package com.mingdao.edge.plugin.gateway.holder;

import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.plugin.gateway.constants.CacheKey;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public class TenantHolder {
    private static Long SYS_ORG = null;

    public static Long getSysOrg() {
        if (SYS_ORG == null) {
            EdgeCacheManager edgeCacheManager = SpringContextUtils.getBean(EdgeCacheManager.class);
            SYS_ORG = (Long) edgeCacheManager.get(CacheKey.SYS_ORG);
        }
        return SYS_ORG;
    }

    public static void setSysOrg(Long sysOrg) {
        EdgeCacheManager edgeCacheManager = SpringContextUtils.getBean(EdgeCacheManager.class);
        edgeCacheManager.set(CacheKey.SYS_ORG, sysOrg);
        SYS_ORG = sysOrg;
    }
}
