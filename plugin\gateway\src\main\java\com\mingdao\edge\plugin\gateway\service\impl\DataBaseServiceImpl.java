package com.mingdao.edge.plugin.gateway.service.impl;

import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mingdao.edge.plugin.api.mapper.DynamicSqlMapper;
import com.mingdao.edge.plugin.api.mapper.service.DataBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态SQL服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Slf4j
@Service
@SofaService(bindings = {@SofaServiceBinding(serialize = false)})
@RequiredArgsConstructor
public class DataBaseServiceImpl implements DataBaseService {

    private final DynamicSqlMapper dynamicSqlMapper;

    @Override
    public List<Map<String, Object>> select(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            List<Map<String, Object>> result = dynamicSqlMapper.selectBySql(sql, params);
            log.debug("执行查询SQL成功，返回{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("执行查询SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行查询SQL失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<Map<String, Object>> selectPageBySql(String sql, Integer pageNum, Integer pageSize, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);

        // 参数校验
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 构建分页对象
        Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);

        try {
            IPage<Map<String, Object>> result = dynamicSqlMapper.selectPageBySql(sql, page, params);
            long count = count(sql, params);
            result.setTotal(count);
            log.debug("执行分页查询SQL成功，总记录数：{}，当前页数据量：{}",
                    result.getTotal(), result.getRecords().size());
            return result;
        } catch (Exception e) {
            log.error("执行分页查询SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行分页查询SQL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            // 检查SQL是否为INSERT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("INSERT INTO")) {
                throw new IllegalArgumentException("SQL语句必须是INSERT语句");
            }

            // 确保params中有id字段
            if (!params.containsKey("id")) {
                params.put("id", null);
            }

            int rows = dynamicSqlMapper.insertBySql(sql, params);
            log.debug("执行插入SQL成功，影响{}行", rows);

            // 获取生成的主键ID
            Long generatedId = (Long) params.get("id");
            log.debug("生成的主键ID: {}", generatedId);
            return generatedId;
        } catch (Exception e) {
            log.error("执行插入SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行插入SQL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            int rows = dynamicSqlMapper.updateBySql(sql, params);
            log.debug("执行更新SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行更新SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行更新SQL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            int rows = dynamicSqlMapper.deleteBySql(sql, params);
            log.debug("执行删除SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行删除SQL失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行删除SQL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(String tableName, List<Long> ids) {
        try {
            int rows = dynamicSqlMapper.deleteByIds(tableName, ids);
            log.debug("执行删除SQL成功，影响{}行", rows);
            return rows;
        } catch (Exception e) {
            log.error("执行删除SQL失败，SQL: {}, 参数: {}", tableName, ids, e);
            throw new RuntimeException("执行删除SQL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void truncateTable(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }
        String sql = "TRUNCATE TABLE " + tableName;
        try {
            dynamicSqlMapper.updateBySql(sql, new HashMap<>());
            log.debug("清空表{}数据成功", tableName);
        } catch (Exception e) {
            log.error("清空表{}数据失败", tableName, e);
            throw new RuntimeException("清空表数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTable(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);

        // 验证SQL是否为CREATE TABLE语句
        String normalizedSql = sql.trim().toUpperCase();
        if (!normalizedSql.startsWith("CREATE TABLE")) {
            throw new IllegalArgumentException("SQL语句必须是CREATE TABLE语句");
        }

        try {
            dynamicSqlMapper.updateBySql(sql, params);
            log.debug("创建表成功，SQL: {}", sql);
            return true;
        } catch (Exception e) {
            log.error("创建表失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("创建表失败: " + e.getMessage());
        }
    }

    /**
     * 校验SQL语句
     */
    private void validateSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }
    }

    /**
     * 校验并处理参数
     */
    private Map<String, Object> validateParams(Map<String, Object> params) {
        return params == null ? new HashMap<>() : params;
    }

    @Override
    public boolean exists(String tableName, String primaryKey, Object primaryValue) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }
        if (primaryKey == null || primaryKey.trim().isEmpty()) {
            throw new IllegalArgumentException("主键列名不能为空");
        }
        if (primaryValue == null) {
            throw new IllegalArgumentException("主键值不能为空");
        }

        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put(primaryKey, primaryValue);

            // 构建查询语句
            String sql = "SELECT 1 FROM " + tableName + " WHERE " + primaryKey + " = #{params." + primaryKey + "} LIMIT 1";

            List<Map<String, Object>> result = dynamicSqlMapper.selectBySql(sql, params);
            boolean exists = result != null && !result.isEmpty();
            log.debug("检查表{}中主键{}={}的记录是否存在，结果: {}", tableName, primaryKey, primaryValue, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查表{}中主键{}={}的记录是否存在失败", tableName, primaryKey, primaryValue, e);
            throw new RuntimeException("检查数据是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> selectOne(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            // 检查SQL是否为SELECT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("SELECT")) {
                throw new IllegalArgumentException("SQL语句必须是SELECT语句");
            }

            // 添加LIMIT 1
            if (!normalizedSql.contains("LIMIT")) {
                sql = sql + " LIMIT 1";
            }

            List<Map<String, Object>> result = dynamicSqlMapper.selectBySql(sql, params);
            if (result == null || result.isEmpty()) {
                log.debug("查询单行数据未找到记录，SQL: {}", sql);
                return null;
            }

            Map<String, Object> row = result.get(0);
            log.debug("查询单行数据成功，SQL: {}", sql);
            return row;
        } catch (Exception e) {
            log.error("查询单行数据失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("查询单行数据失败: " + e.getMessage());
        }
    }

    @Override
    public long count(String sql, Map<String, Object> params) {
        validateSql(sql);
        params = validateParams(params);
        try {
            // 检查SQL是否为SELECT语句
            String normalizedSql = sql.trim().toUpperCase();
            if (!normalizedSql.startsWith("SELECT")) {
                throw new IllegalArgumentException("SQL语句必须是SELECT语句");
            }

            // 将SQL转换为COUNT查询
            String countSql = "SELECT COUNT(*) as TOTAL FROM (" + sql + ") as temp_table";

            List<Map<String, Object>> result = dynamicSqlMapper.selectBySql(countSql, params);
            if (result == null || result.isEmpty()) {
                log.debug("执行COUNT查询未找到记录，SQL: {}", countSql);
                return 0L;
            }

            Object count = result.get(0).get("TOTAL");
            if (count == null) {
                log.warn("COUNT查询结果为空，SQL: {}", countSql);
                return 0L;
            }

            long total = count instanceof Number ? ((Number) count).longValue() : 0L;
            log.debug("执行COUNT查询成功，总数: {}, SQL: {}", total, countSql);
            return total;
        } catch (Exception e) {
            log.error("执行COUNT查询失败，SQL: {}, 参数: {}", sql, params, e);
            throw new RuntimeException("执行COUNT查询失败: " + e.getMessage());
        }
    }
}
