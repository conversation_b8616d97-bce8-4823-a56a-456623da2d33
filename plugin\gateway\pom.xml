<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mingdao.edge</groupId>
        <artifactId>plugin</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.mingdao.edge.plugin</groupId>
    <artifactId>gateway</artifactId>
    <name>${artifactId}</name>

    <dependencies>
        <!--project artifact-->
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-service</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>biz-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>ark-event</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-cache</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>ark-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-biz-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>mapper-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-disruptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.plugin</groupId>
            <artifactId>gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mingdao.edge.core</groupId>
            <artifactId>common-mqtt</artifactId>
        </dependency>
        <!--end project artifact-->

        <!-- start to add sofa-ark-plugin -->
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>healthcheck-sofa-boot-starter</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-sofa-boot-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>runtime-sofa-boot-starter</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-all</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-compatible-springboot2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>sofa-ark-springboot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alipay.sofa</groupId>
                    <artifactId>sofa-ark-compatible-springboot1</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>web-ark-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>hessian</artifactId>
        </dependency>
        <!-- end to add sofa-ark-plugin -->

        <!--tools-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <scope>compile</scope>
        </dependency>
        <!--<dependency>-->
        <!--    <groupId>org.apache.httpcomponents</groupId>-->
        <!--    <artifactId>httpcore</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.apache.httpcomponents</groupId>-->
        <!--    <artifactId>httpclient</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>com.google.code.gson</groupId>-->
        <!--    <artifactId>gson</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.mqttv5.client</artifactId>
        </dependency>
        <!--end tools-->

        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- end spring -->

        <!-- database -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>
        <!-- end database -->

        <!-- edge-client和edge-server的共同包 -->
        <!--<dependency>-->
        <!--    <groupId>com.yhlz.iot.core.common</groupId>-->
        <!--    <artifactId>common-edge</artifactId>-->
        <!--    <scope>compile</scope>-->
        <!--</dependency>-->

    </dependencies>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
            </testResource>
        </testResources>

        <plugins>
            <!-- 这里配置动态模块打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <!--<configuration>-->
                <!--    <outputDirectory>${user.dir}/target</outputDirectory>-->
                <!--    <classifier>ark-biz</classifier>-->
                <!--</configuration>-->
                <executions>
                    <execution>
                        <id>package</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>${module.install.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>${module.deploy.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>${skipTests}</skipTests>
                    <includes>
                        <!-- 这里需要根据自己的需要指定要跑的单元测试 -->
                        <include>**/*Test.java</include>
                    </includes>
                    <!-- 如无特殊需求，将forkMode设置为once -->
                    <forkMode>once</forkMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
