package com.mingdao.edge.plugin.data.sync.config;

import cn.hutool.core.util.StrUtil;
import com.alipay.sofa.runtime.api.annotation.SofaReference;
import com.mingdao.edge.core.common.biz.event.EventHandlerUtils;
import com.mingdao.edge.core.common.cache.EdgeCacheManager;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.mqtt.utils.RegisterMqttClientUtil;
import com.mingdao.edge.plugin.api.gateway.ServerApi;
import com.mingdao.edge.plugin.api.gateway.dto.TenantMqttProperty;
import com.mingdao.edge.plugin.data.sync.handler.OnMqttMessageCallback;
import com.mingdao.edge.plugin.data.sync.service.impl.SyncTaskServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Collections;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Component
public class StartupApplicationListener {

    @Resource
    private OnMqttMessageCallback onMqttMessageCallback;
    @Resource
    private EdgeCacheManager edgeCacheManager;
    @Resource
    private SyncTaskServiceImpl syncTaskServiceImpl;

    @SofaReference
    private ServerApi serverApi;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {

        EventHandlerUtils.initEventHandler();

        TenantMqttProperty tenantMqttProperty = serverApi.getTenantMqttProperty();

        try {
            RegisterMqttClientUtil.registerMqttClient(
                    tenantMqttProperty.getBroker(),
                    SpringContextUtils.getApplicationName() + "-" + tenantMqttProperty.getUserId(),
                    tenantMqttProperty.getUserName(),
                    tenantMqttProperty.getPassword(),
                    Collections.singletonList(StrUtil.format("edge/data/down/{}/#", tenantMqttProperty.getOrgCode())),
                    onMqttMessageCallback);

            syncTaskServiceImpl.init();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void beforeDestroy() {
        edgeCacheManager.clear();
    }
}
