<?xml version="1.0" encoding="UTF-8"?>
<!--
    小技巧: 在根pom里面设置统一存放路径，统一管理方便维护
    <properties>
        <log-path>/Users/<USER>/log-path>
    </properties>
    1. 其他模块加日志输出，直接copy本文件放在resources 目录即可
    2. 注意修改 <property name="${log-path}/log.path" value=""/> 的value模块
-->
<configuration debug="false" scan="false">
	<!-- mybatis操作日志 -->
	<logger name="com.mingdao" level="INFO"/>
	<logger name="com.caucho.hessian" level="ERROR" />

	<!--<appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
		<destination>${logstashIP}</destination>
		<encoder class="net.logstash.logback.encoder.LogstashEncoder" >
			<includeCallerData>true</includeCallerData>

		</encoder>
	</appender>-->

	<!-- level appender -->
	<include resource="default.xml"/>
	<include resource="console-appender.xml"/>
	<include resource="all-appender.xml"/>
	<include resource="error-appender.xml"/>

	<root level="INFO">
		<appender-ref ref="CONSOLE"/>
		<appender-ref ref="ALL"/>
		<appender-ref ref="ERROR"/>
		<!--<appender-ref ref="LOGSTASH"/>-->
	</root>
</configuration>
