<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mingdao</groupId>
        <artifactId>edge</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>com.mingdao.edge</groupId>
    <artifactId>core</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>common-biz-context</module>
        <module>common</module>
        <module>common-biz-event</module>
        <module>common-cache</module>
        <module>common-mqtt</module>
        <module>common-http</module>
        <module>common-disruptor</module>
        <module>common-service</module>
        <module>biz-api</module>
        <module>ark-event</module>
    </modules>

    <dependencyManagement>
        <dependencies>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>${module.install.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>${module.deploy.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>${skipTests}</skipTests>
                    <includes>
                        <!-- 这里需要根据自己的需要指定要跑的单元测试 -->
                        <include>**/*Test.java</include>
                    </includes>
                    <!-- 如无特殊需求，将forkMode设置为once -->
                    <forkMode>once</forkMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
