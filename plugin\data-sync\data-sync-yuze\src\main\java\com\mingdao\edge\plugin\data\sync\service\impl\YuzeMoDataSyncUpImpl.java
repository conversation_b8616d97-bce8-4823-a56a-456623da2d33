package com.mingdao.edge.plugin.data.sync.service.impl;

import com.alipay.sofa.runtime.api.annotation.SofaService;
import com.mingdao.edge.plugin.api.data.sync.service.DataSyncService;
import com.mingdao.edge.plugin.data.sync.base.constants.DataSyncConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 生产订单
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Service
@SofaService(uniqueId = DataSyncConstants.DATA_SYNC_SERVICE + "_yuze_moupload")
public class YuzeMoDataSyncUpImpl extends AbstractYuzeDataSyncUp implements DataSyncService {

    public YuzeMoDataSyncUpImpl() {
        super("yuze", "moupload", DataSyncConstants.TOPIC_MO, 1000);
    }
}
