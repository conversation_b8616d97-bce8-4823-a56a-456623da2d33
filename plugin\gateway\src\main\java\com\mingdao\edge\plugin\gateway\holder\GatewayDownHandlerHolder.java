package com.mingdao.edge.plugin.gateway.holder;

import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.common.service.AbstractReferenceServiceHolder;
import com.mingdao.edge.plugin.api.gateway.GatewayDownHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc desc
 */
@Slf4j
@Component
public class GatewayDownHandlerHolder extends AbstractReferenceServiceHolder<GatewayDownHandler> {

    /**
     * 初始化 gateway 基座命令处理器
     */
    public void init() {
        try {
            Map<String, GatewayDownHandler> map = SpringContextUtils.getBeansOfType(GatewayDownHandler.class);
            if(map != null && !map.isEmpty()) {
                map.entrySet().forEach(a->  {
                    servicesMap.put(a.getKey(), a.getValue());
                    log.info("已加载指令处理器:{}", a);
                });
            }
        }
        catch (BeansException e) {
            log.error("初始化指令处理器出现异常", e);
        }
    }

}
